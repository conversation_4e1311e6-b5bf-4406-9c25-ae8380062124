.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-image: linear-gradient(to bottom, #2e026d, #15162c);
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  padding: 4rem 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.title {
  font-size: 3rem;
  line-height: 1;
  font-weight: 800;
  letter-spacing: -0.025em;
  margin: 0;
  color: white;
}

@media (min-width: 640px) {
  .title {
    font-size: 5rem;
  }
}

.pinkSpan {
  color: hsl(280 100% 70%);
}

.cardRow {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 640px) {
  .cardRow {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .cardRow {
    gap: 2rem;
  }
}

.card {
  max-width: 20rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  color: white;
  background-color: rgb(255 255 255 / 0.1);
}

.card:hover {
  background-color: rgb(255 255 255 / 0.2);
  transition: background-color 150ms cubic-bezier(0.5, 0, 0.2, 1);
}

.cardTitle {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  margin: 0;
}

.cardText {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.showcaseContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.showcaseText {
  color: white;
  text-align: center;
  font-size: 1.5rem;
  line-height: 2rem;
}

.authContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.loginButton {
  border-radius: 9999px;
  background-color: rgb(255 255 255 / 0.1);
  padding: 0.75rem 2.5rem;
  font-weight: 600;
  color: white;
  text-decoration-line: none;
  transition: background-color 150ms cubic-bezier(0.5, 0, 0.2, 1);
}

.loginButton:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input {
  width: 100%;
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  color: black;
}

.submitButton {
  all: unset;
  border-radius: 9999px;
  background-color: rgb(255 255 255 / 0.1);
  padding: 0.75rem 2.5rem;
  font-weight: 600;
  color: white;
  text-align: center;
  transition: background-color 150ms cubic-bezier(0.5, 0, 0.2, 1);
}

.submitButton:hover {
  background-color: rgb(255 255 255 / 0.2);
}
