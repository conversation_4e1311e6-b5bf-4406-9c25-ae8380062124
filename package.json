{"name": "nextmall", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo && tsc --noEmit", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@chakra-ui/react": "^3.22.0", "@prisma/client": "^6.5.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "bcryptjs": "^3.0.2", "china-division": "^2.7.0", "jsonwebtoken": "^9.0.2", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.14.10", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "prettier": "^3.5.3", "prisma": "^6.5.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.13.1"}